/* 悬停在折叠菜单图标上时放大 */
.ant-menu-inline-collapsed .ant-menu-item .ant-menu-item-icon {
  transition: transform 0.2s ease;
}
.ant-menu-inline-collapsed .ant-menu-item:hover .ant-menu-item-icon {
  transform: scale(1.2);
}

/* 收起状态下菜单项图标居中对齐 */
.ant-layout-sider-collapsed .ant-menu-item {
  padding-left: 0 !important;
  padding-right: 0 !important;
  text-align: center;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.ant-layout-sider-collapsed .ant-menu-item .ant-menu-item-icon {
  margin-inline-end: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
}

.ant-layout-sider-collapsed .ant-menu-item .ant-menu-title-content {
  display: none !important;
}

/* 确保图标在垂直方向也居中 */
.ant-layout-sider-collapsed .ant-menu-item-icon svg {
  display: block !important;
  margin: 0 auto !important;
}

.main-layout {
  height: 100vh;
  overflow: hidden;
}

.main-layout-sider {
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
}

.logo-area {
  height: 64px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.logo-area-expanded {
  justify-content: space-between;
  padding: 0 24px;
  cursor: default;
}

.logo-area-collapsed {
  justify-content: center;
  padding: 0;
  cursor: pointer;
}

.logo-inner {
  display: flex;
  align-items: center;
}

.logo-inner-expanded {
  flex: 1;
  justify-content: flex-start;
}

.logo-inner-collapsed {
  flex: none;
  justify-content: center;
}

.logo-img {
  height: 36px;
  width: 36px;
  object-fit: contain;
  transition: all 0.2s ease;
}

.logo-title {
  margin: 0 0 0 10px !important; /* Use !important to override Ant Design styles */
  color: #000000;
  transition: opacity 0.2s ease, width 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
}

.logo-title-hidden {
  opacity: 0;
  width: 0 !important;
  margin: 0 !important;
}

.logo-img-wrapper-collapsed {
  position: relative;
}

.logo-img-collapsed {
  height: 40px;
  width: 40px;
  object-fit: contain;
  transition: all 0.2s ease;
}

.logo-img-collapsed:hover {
  transform: scale(1.1);
}

/* 菜单图标大小控制 */
.ant-layout-sider .ant-menu-item .ant-menu-item-icon {
  font-size: 18px;
}

.ant-layout-sider-collapsed .ant-menu-item .ant-menu-item-icon {
  font-size: 24px;
}